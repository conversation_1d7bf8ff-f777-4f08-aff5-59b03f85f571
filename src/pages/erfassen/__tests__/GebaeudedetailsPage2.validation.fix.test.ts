import { z } from 'zod';

// Import the schemas from the component
const bauteilSchema = z.object({
  id: z.string(),
  bezeichnung: z.string().min(1, 'Bezeichnung ist erforderlich'),
  massiv: z.string().optional(),
  uebergang: z.string().optional(),
  flaeche: z.string().min(1, 'Fläche ist erforderlich'),
  uWert: z.string().optional(),
  wallWidth: z.string().optional(),
  numberOfLevels: z.string().optional(),
});

const gebaeudedetails2Schema = z.object({
  // Gebäudedetails Teil 2
  kuehlWfl: z.string().default('0'),
  Originaldaemmstandard: z.enum(['0', '1', '2']).default('0'),
  bjFensterAustausch: z.string().optional(),
  Fensterlüftung: z.enum(['0', '1']).default('0'),
  Schachtlüftung: z.enum(['0', '1']).default('0'),
  L_Mit_WRG: z.enum(['0', '1']).default('0'),
  L_Ohne_WRG: z.enum(['0', '1']).default('0'),

  // Moved from GebaeudedetailsPage1
  Keller_beheizt: z.enum(['0', '1']).default('0').optional(),
  Klimatisiert: z.enum(['0', '1']).default('0'),

  // Erneuerbare Energien Felder (von TwwLueftungPage verschoben)
  TW_Solar: z.enum(['0', '1']).default('0'),
  HZ_Solar: z.enum(['0', '1']).default('0'),
  TW_WP: z.enum(['0', '1']).default('0'),
  HZ_WP: z.enum(['0', '1']).default('0'),

  // Dämmungsfelder für alle Zertifikatstypen
  Boden1_Dämmung: z.string().default('0'),
  Dach1_Dämmung: z.string().default('0'),
  Wand1_Dämmung: z.string().default('0'),

  // Bauteile (dynamisch)
  boeden: z.array(bauteilSchema).default([]),
  daecher: z.array(bauteilSchema).default([]),
  waende: z.array(bauteilSchema).default([]),
});

type Gebaeudedetails2FormValues = z.infer<typeof gebaeudedetails2Schema>;

describe('GebaeudedetailsPage2 Validation Fix Tests', () => {
  const baseValidData: Gebaeudedetails2FormValues = {
    kuehlWfl: '0',
    Originaldaemmstandard: '0',
    bjFensterAustausch: '',
    Fensterlüftung: '0',
    Schachtlüftung: '0',
    L_Mit_WRG: '0',
    L_Ohne_WRG: '0',
    Keller_beheizt: '0',
    Klimatisiert: '0',
    TW_Solar: '0',
    HZ_Solar: '0',
    TW_WP: '0',
    HZ_WP: '0',
    Boden1_Dämmung: '0',
    Dach1_Dämmung: '0',
    Wand1_Dämmung: '0',
    boeden: [],
    daecher: [],
    waende: [],
  };

  test('should pass validation with empty arrays for building components', () => {
    const testData = {
      ...baseValidData,
      boeden: [],
      daecher: [],
      waende: [],
    };

    const result = gebaeudedetails2Schema.safeParse(testData);
    expect(result.success).toBe(true);
  });

  test('should pass validation with valid building component arrays', () => {
    const testData = {
      ...baseValidData,
      boeden: [
        {
          id: 'test-boden-1',
          bezeichnung: 'Test Boden',
          massiv: 'kb_massiv',
          uebergang: '1',
          flaeche: '100',
          uWert: '0.5',
          wallWidth: '',
          numberOfLevels: ''
        }
      ],
      daecher: [
        {
          id: 'test-dach-1',
          bezeichnung: 'Test Dach',
          massiv: '0',
          uebergang: '0',
          flaeche: '75',
          uWert: '0.3',
          wallWidth: '',
          numberOfLevels: ''
        }
      ],
      waende: [
        {
          id: 'test-wand-1',
          bezeichnung: 'Test Wand',
          massiv: 'kb_massiv',
          uebergang: '',
          flaeche: '50',
          uWert: '0.4',
          wallWidth: '',
          numberOfLevels: ''
        }
      ],
    };

    const result = gebaeudedetails2Schema.safeParse(testData);
    expect(result.success).toBe(true);
  });

  test('should fail validation when building components are objects instead of arrays', () => {
    const testDataWithObjects = {
      ...baseValidData,
      // These should be arrays but are objects - this should fail
      boeden: { id: 'test', bezeichnung: 'test' } as any,
      daecher: { id: 'test', bezeichnung: 'test' } as any,
      waende: { id: 'test', bezeichnung: 'test' } as any,
    };

    const result = gebaeudedetails2Schema.safeParse(testDataWithObjects);
    expect(result.success).toBe(false);
    
    if (!result.success) {
      const fieldErrors = result.error.flatten().fieldErrors;
      expect(fieldErrors.boeden).toBeDefined();
      expect(fieldErrors.daecher).toBeDefined();
      expect(fieldErrors.waende).toBeDefined();
      
      // Check that the error messages indicate array was expected
      expect(fieldErrors.boeden?.[0]).toContain('Expected array');
      expect(fieldErrors.daecher?.[0]).toContain('Expected array');
      expect(fieldErrors.waende?.[0]).toContain('Expected array');
    }
  });

  test('should fail validation when required fields in building components are missing', () => {
    const testData = {
      ...baseValidData,
      waende: [
        {
          id: 'test-1',
          bezeichnung: '', // Empty bezeichnung - should cause validation error
          massiv: 'kb_massiv',
          uebergang: '',
          flaeche: '', // Empty flaeche - should cause validation error
          uWert: '',
          wallWidth: '',
          numberOfLevels: ''
        }
      ]
    };

    const result = gebaeudedetails2Schema.safeParse(testData);
    expect(result.success).toBe(false);
    
    if (!result.success) {
      const fieldErrors = result.error.flatten().fieldErrors;
      expect(fieldErrors.waende).toBeDefined();
      // Should contain validation errors for both bezeichnung and flaeche
      expect(fieldErrors.waende?.some(error => error.includes('Bezeichnung ist erforderlich'))).toBe(true);
      expect(fieldErrors.waende?.some(error => error.includes('Fläche ist erforderlich'))).toBe(true);
    }
  });

  test('should handle safe array value extraction', () => {
    // Test the logic that would be used in getSafeArrayValue function
    const testFormValue = undefined;
    const testFallbackValue = [{ id: 'test', bezeichnung: 'test', flaeche: '100' }];

    // Simulate the logic from getSafeArrayValue
    let result;
    if (Array.isArray(testFormValue)) {
      result = testFormValue;
    } else if (Array.isArray(testFallbackValue)) {
      result = testFallbackValue;
    } else {
      result = [];
    }

    expect(Array.isArray(result)).toBe(true);
    expect(result).toEqual(testFallbackValue);
  });

  test('should handle non-array form values safely', () => {
    // Test with object instead of array (the original problem)
    const testFormValue = { id: 'test', bezeichnung: 'test' };
    const testFallbackValue = [{ id: 'fallback', bezeichnung: 'fallback', flaeche: '50' }];

    // Simulate the logic from getSafeArrayValue
    let result;
    if (Array.isArray(testFormValue)) {
      result = testFormValue;
    } else if (Array.isArray(testFallbackValue)) {
      result = testFallbackValue;
    } else {
      result = [];
    }

    expect(Array.isArray(result)).toBe(true);
    expect(result).toEqual(testFallbackValue);
    expect(typeof result.map).toBe('function'); // Ensure map function exists
  });
});
